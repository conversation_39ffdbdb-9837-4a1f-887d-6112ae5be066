<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="t('action.import')" @ok="handleSubmit" :showOkBtn="!resultData">
    <BasicForm @register="registerForm" />
    <div v-if="resultData" class="mt-4">
      <Divider>导入结果</Divider>
      <Descriptions :column="1" bordered>
        <Descriptions.Item label="创建数量">{{ resultData.createCount }}</Descriptions.Item>
        <Descriptions.Item label="更新数量">{{ resultData.updateCount }}</Descriptions.Item>
        <Descriptions.Item label="失败数量">{{ resultData.failureCount }}</Descriptions.Item>
        <Descriptions.Item label="总数量">{{ resultData.totalCount }}</Descriptions.Item>
      </Descriptions>
      <div v-if="resultData.failureList && resultData.failureList.length > 0" class="mt-4">
        <Divider>失败详情</Divider>
        <Table :dataSource="resultData.failureList" :columns="failureColumns" size="small" bordered />
      </div>
      <div class="mt-4 text-center">
        <a-button @click="handleReImport">重新导入</a-button>
        <a-button class="ml-4" type="primary" @click="handleClose">关闭</a-button>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { BasicModal, useModalInner } from '@/components/Modal'
import { BasicForm, useForm } from '@/components/Form'
import { importUser } from '@/api/medsciUsers'
import { Divider, Descriptions, Table } from 'ant-design-vue'

defineOptions({ name: 'UserImportModal' })

const emit = defineEmits(['success', 'register'])

const { t } = useI18n()
const { createMessage } = useMessage()

const resultData = ref(null)

const failureColumns = [
  {
    title: '行号',
    dataIndex: 'rowIndex',
    width: 60
  },
  {
    title: '失败原因',
    dataIndex: 'message',
    width: 200
  }
]

const [registerForm, { getFieldsValue, validate, resetFields }] = useForm({
  labelWidth: 120,
  schemas: [
    {
      label: '上传文件',
      field: 'file',
      required: true,
      component: 'Upload',
      componentProps: {
        maxNumber: 1,
        accept: ['.xls','.xlsx']
      }
    }
  ]
})

const [registerModal, { setModalProps, closeModal }] = useModalInner(() => {
  resetFields()
  resultData.value = null
})

async function handleSubmit() {
  try {
    await validate()
    const values = getFieldsValue()
    if (!values.file || values.file.length === 0) {
      createMessage.warning('请上传文件')
      return
    }
    
    setModalProps({ confirmLoading: true })
    const formData = new FormData()
    formData.append('file', values.file[0].originFileObj)
    
    const res = await importUser(formData)
    resultData.value = res
    createMessage.success('导入成功')
    emit('success')
  } catch (error) {
    console.error('导入失败:', error)
    createMessage.error('导入失败: ' + (error?.message || '未知错误'))
  } finally {
    setModalProps({ confirmLoading: false })
  }
}

function handleReImport() {
  resultData.value = null
  resetFields()
}

function handleClose() {
  closeModal()
}
</script>
<style scoped>
.mt-4 {
  margin-top: 1rem;
}

.ml-4 {
  margin-left: 1rem;
}
</style>