import{_ as pa}from"./Clki7XNQ.js";import{al as Wt,r as y,am as ba,an as Ft,e as Jt,d as $e,a5 as qe,j as M,J as ae,z as p,ag as ge,o as lt,ao as Kt,ap as wa,aq as _a,ar as ka,as as Ia,O as ut,at as xa,V as Ta,au as Sa,G as Ca,E as me,D as $a,u as Ra,H as Ot,F as Aa,aj as Bt,ak as Oa,$ as Ba,a3 as et,a4 as Ea,g as Pe,t as O,v as C,x as N,U as Ie,K as te,L as u,M as xe,N as Ue,A as P,B as oe,y as Z,av as tt,aw as at,Y as Na,ax as Pa,Q as Et,a0 as he,ay as Ua,a2 as Da,S as st}from"./B7yib2Q0.js";import{r as dt,c as Yt,b as Xt,d as za,i as Va,o as Zt,m as ye,e as Qt,n as be,f as Re,u as Gt,h as ea,j as ta,k as Ma,l as mt,q as Le,t as De,w as ht,v as aa,x as We,y as pe,z as Ha,A as na,B as Fe,C as ja,D as oa,E as je,F as La,G as it,H as rt,I as Nt,J as ft,K as qa,L as Wa,M as Pt,N as Fa,O as Ja,Q as Ka,R as la,S as Ya,T as Xa,U as Ut,V as Za,g as Dt,p as Qa,a as Ga,P as en}from"./BTDtKNTY.js";import zt from"./DTnerMQa.js";import{a as tn,h as an}from"./DMAjf-Z3.js";import{u as nn,b as on}from"./D8PktKVn.js";import{u as ln}from"./BvTBxqY-.js";import{s as sn}from"./x_rD_Ya3.js";import{_ as rn}from"./DlAUqK2U.js";function cn(e,l,s){let o,a=0;const t=e.scrollLeft,v=s===0?1:Math.round(s*1e3/16);let i=t;function m(){Yt(o)}function b(){i+=(l-t)/v,e.scrollLeft=i,++a<v&&(o=dt(b))}return b(),m}function un(e,l,s,o){let a,t=Xt(e);const v=t<l,i=s===0?1:Math.round(s*1e3/16),m=(l-t)/i;function b(){Yt(a)}function d(){t+=m,(v&&t>l||!v&&t<l)&&(t=l),za(e,t),v&&t<l||!v&&t>l?a=dt(d):o&&(a=dt(o))}return d(),b}let dn=0;function sa(){const e=Wt(),{name:l="unknown"}=(e==null?void 0:e.type)||{};return`${l}-${++dn}`}function fn(){const e=y([]),l=[];return ba(()=>{e.value=[]}),[e,o=>(l[o]||(l[o]=a=>{e.value[o]=a}),l[o])]}function ia(e,l){if(!Va||!window.IntersectionObserver)return;const s=new IntersectionObserver(t=>{l(t[0].intersectionRatio>0)},{root:document.body}),o=()=>{e.value&&s.observe(e.value)},a=()=>{e.value&&s.unobserve(e.value)};Ft(a),Jt(a),Zt(o)}const[vn,mn]=Re("sticky"),hn={zIndex:be,position:Qt("top"),container:Object,offsetTop:ye(0),offsetBottom:ye(0)};var gn=$e({name:vn,props:hn,emits:["scroll","change"],setup(e,{emit:l,slots:s}){const o=y(),a=Gt(o),t=qe({fixed:!1,width:0,height:0,transform:0}),v=y(!1),i=M(()=>ea(e.position==="top"?e.offsetTop:e.offsetBottom)),m=M(()=>{if(v.value)return;const{fixed:k,height:I,width:x}=t;if(k)return{width:`${x}px`,height:`${I}px`}}),b=M(()=>{if(!t.fixed||v.value)return;const k=ta(Ma(e.zIndex),{width:`${t.width}px`,height:`${t.height}px`,[e.position]:`${i.value}px`});return t.transform&&(k.transform=`translate3d(0, ${t.transform}px, 0)`),k}),d=k=>l("scroll",{scrollTop:k,isFixed:t.fixed}),$=()=>{if(!o.value||Le(o))return;const{container:k,position:I}=e,x=De(o),A=Xt(window);if(t.width=x.width,t.height=x.height,I==="top")if(k){const w=De(k),q=w.bottom-i.value-t.height;t.fixed=i.value>x.top&&w.bottom>0,t.transform=q<0?q:0}else t.fixed=i.value>x.top;else{const{clientHeight:w}=document.documentElement;if(k){const q=De(k),R=w-q.top-i.value-t.height;t.fixed=w-i.value<x.bottom&&w>q.top,t.transform=R<0?-R:0}else t.fixed=w-i.value<x.bottom}d(A)};return ae(()=>t.fixed,k=>l("change",k)),mt("scroll",$,{target:a,passive:!0}),ia(o,$),ae([ht,aa],()=>{!o.value||Le(o)||!t.fixed||(v.value=!0,ge(()=>{const k=De(o);t.width=k.width,t.height=k.height,v.value=!1}))}),()=>{var k;return p("div",{ref:o,style:m.value},[p("div",{class:mn({fixed:t.fixed&&!v.value}),style:b.value},[(k=s.default)==null?void 0:k.call(s)])])}}});const yn=We(gn),[ra,nt]=Re("swipe"),pn={loop:pe,width:be,height:be,vertical:Boolean,autoplay:ye(0),duration:ye(500),touchable:pe,lazyRender:Boolean,initialSwipe:ye(0),indicatorColor:String,showIndicators:pe,stopPropagation:pe},ca=Symbol(ra);var bn=$e({name:ra,props:pn,emits:["change","dragStart","dragEnd"],setup(e,{emit:l,slots:s}){const o=y(),a=y(),t=qe({rect:null,width:0,height:0,offset:0,active:0,swiping:!1});let v=!1;const i=Ha(),{children:m,linkChildren:b}=na(ca),d=M(()=>m.length),$=M(()=>t[e.vertical?"height":"width"]),k=M(()=>e.vertical?i.deltaY.value:i.deltaX.value),I=M(()=>t.rect?(e.vertical?t.rect.height:t.rect.width)-$.value*d.value:0),x=M(()=>$.value?Math.ceil(Math.abs(I.value)/$.value):d.value),A=M(()=>d.value*$.value),w=M(()=>(t.active+d.value)%d.value),q=M(()=>{const g=e.vertical?"vertical":"horizontal";return i.direction.value===g}),R=M(()=>{const g={transitionDuration:`${t.swiping?0:e.duration}ms`,transform:`translate${e.vertical?"Y":"X"}(${+t.offset.toFixed(2)}px)`};if($.value){const D=e.vertical?"height":"width",U=e.vertical?"width":"height";g[D]=`${A.value}px`,g[U]=e[U]?`${e[U]}px`:""}return g}),le=g=>{const{active:D}=t;return g?e.loop?it(D+g,-1,d.value):it(D+g,0,x.value):D},z=(g,D=0)=>{let U=g*$.value;e.loop||(U=Math.min(U,-I.value));let Q=D-U;return e.loop||(Q=it(Q,I.value,0)),Q},H=({pace:g=0,offset:D=0,emitChange:U})=>{if(d.value<=1)return;const{active:Q}=t,c=le(g),T=z(c,D);if(e.loop){if(m[0]&&T!==I.value){const S=T<I.value;m[0].setOffset(S?A.value:0)}if(m[d.value-1]&&T!==0){const S=T>0;m[d.value-1].setOffset(S?-A.value:0)}}t.active=c,t.offset=T,U&&c!==Q&&l("change",w.value)},J=()=>{t.swiping=!0,t.active<=-1?H({pace:d.value}):t.active>=d.value&&H({pace:-d.value})},ee=()=>{J(),i.reset(),je(()=>{t.swiping=!1,H({pace:-1,emitChange:!0})})},L=()=>{J(),i.reset(),je(()=>{t.swiping=!1,H({pace:1,emitChange:!0})})};let W;const K=()=>clearTimeout(W),j=()=>{K(),+e.autoplay>0&&d.value>1&&(W=setTimeout(()=>{L(),j()},+e.autoplay))},ne=(g=+e.initialSwipe)=>{if(!o.value)return;const D=()=>{var U,Q;if(!Le(o)){const c={width:o.value.offsetWidth,height:o.value.offsetHeight};t.rect=c,t.width=+((U=e.width)!=null?U:c.width),t.height=+((Q=e.height)!=null?Q:c.height)}d.value&&(g=Math.min(d.value-1,g),g===-1&&(g=d.value-1)),t.active=g,t.swiping=!0,t.offset=z(g),m.forEach(c=>{c.setOffset(0)}),j()};Le(o)?ge().then(D):D()},Y=()=>ne(t.active);let ce;const Te=g=>{!e.touchable||g.touches.length>1||(i.start(g),v=!1,ce=Date.now(),K(),J())},Se=g=>{e.touchable&&t.swiping&&(i.move(g),q.value&&(!e.loop&&(t.active===0&&k.value>0||t.active===d.value-1&&k.value<0)||(La(g,e.stopPropagation),H({offset:k.value}),v||(l("dragStart",{index:w.value}),v=!0))))},ze=()=>{if(!e.touchable||!t.swiping)return;const g=Date.now()-ce,D=k.value/g;if((Math.abs(D)>.25||Math.abs(k.value)>$.value/2)&&q.value){const Q=e.vertical?i.offsetY.value:i.offsetX.value;let c=0;e.loop?c=Q>0?k.value>0?-1:1:0:c=-Math[k.value>0?"ceil":"floor"](k.value/$.value),H({pace:c,emitChange:!0})}else k.value&&H({pace:0});v=!1,t.swiping=!1,l("dragEnd",{index:w.value}),j()},Ve=(g,D={})=>{J(),i.reset(),je(()=>{let U;e.loop&&g===d.value?U=t.active===0?0:g:U=g%d.value,D.immediate?je(()=>{t.swiping=!1}):t.swiping=!1,H({pace:U-t.active,emitChange:!0})})},Ae=(g,D)=>{const U=D===w.value,Q=U?{backgroundColor:e.indicatorColor}:void 0;return p("i",{style:Q,class:nt("indicator",{active:U})},null)},Me=()=>{if(s.indicator)return s.indicator({active:w.value,total:d.value});if(e.showIndicators&&d.value>1)return p("div",{class:nt("indicators",{vertical:e.vertical})},[Array(d.value).fill("").map(Ae)])};return Fe({prev:ee,next:L,state:t,resize:Y,swipeTo:Ve}),b({size:$,props:e,count:d,activeIndicator:w}),ae(()=>e.initialSwipe,g=>ne(+g)),ae(d,()=>ne(t.active)),ae(()=>e.autoplay,j),ae([ht,aa,()=>e.width,()=>e.height],Y),ae(ja(),g=>{g==="visible"?j():K()}),lt(ne),Kt(()=>ne(t.active)),oa(()=>ne(t.active)),Ft(K),Jt(K),mt("touchmove",Se,{target:a}),()=>{var g;return p("div",{ref:o,class:nt()},[p("div",{ref:a,style:R.value,class:nt("track",{vertical:e.vertical}),onTouchstartPassive:Te,onTouchend:ze,onTouchcancel:ze},[(g=s.default)==null?void 0:g.call(s)]),Me()])}}});const wn=We(bn),[_n,Vt]=Re("tabs");var kn=$e({name:_n,props:{count:rt(Number),inited:Boolean,animated:Boolean,duration:rt(be),swipeable:Boolean,lazyRender:Boolean,currentIndex:rt(Number)},emits:["change"],setup(e,{emit:l,slots:s}){const o=y(),a=i=>l("change",i),t=()=>{var i;const m=(i=s.default)==null?void 0:i.call(s);return e.animated||e.swipeable?p(wn,{ref:o,loop:!1,class:Vt("track"),duration:+e.duration*1e3,touchable:e.swipeable,lazyRender:e.lazyRender,showIndicators:!1,onChange:a},{default:()=>[m]}):m},v=i=>{const m=o.value;m&&m.state.active!==i&&m.swipeTo(i,{immediate:!e.inited})};return ae(()=>e.currentIndex,v),lt(()=>{v(e.currentIndex)}),Fe({swipeRef:o}),()=>p("div",{class:Vt("content",{animated:e.animated||e.swipeable})},[t()])}});const[ua,ot]=Re("tabs"),In={type:Qt("line"),color:String,border:Boolean,sticky:Boolean,shrink:Boolean,active:ye(0),duration:ye(.3),animated:Boolean,ellipsis:pe,swipeable:Boolean,scrollspy:Boolean,offsetTop:ye(0),background:String,lazyRender:pe,showHeader:pe,lineWidth:be,lineHeight:be,beforeChange:Function,swipeThreshold:ye(5),titleActiveColor:String,titleInactiveColor:String},da=Symbol(ua);var xn=$e({name:ua,props:In,emits:["change","scroll","rendered","clickTab","update:active"],setup(e,{emit:l,slots:s}){let o,a,t,v,i;const m=y(),b=y(),d=y(),$=y(),k=sa(),I=Gt(m),[x,A]=fn(),{children:w,linkChildren:q}=na(da),R=qe({inited:!1,position:"",lineStyle:{},currentIndex:-1}),le=M(()=>w.length>+e.swipeThreshold||!e.ellipsis||e.shrink),z=M(()=>({borderColor:e.color,background:e.background})),H=(c,T)=>{var S;return(S=c.name)!=null?S:T},J=M(()=>{const c=w[R.currentIndex];if(c)return H(c,R.currentIndex)}),ee=M(()=>ea(e.offsetTop)),L=M(()=>e.sticky?ee.value+o:0),W=c=>{const T=b.value,S=x.value;if(!le.value||!T||!S||!S[R.currentIndex])return;const B=S[R.currentIndex].$el,E=B.offsetLeft-(T.offsetWidth-B.offsetWidth)/2;v&&v(),v=cn(T,E,c?0:+e.duration)},K=()=>{const c=R.inited;ge(()=>{const T=x.value;if(!T||!T[R.currentIndex]||e.type!=="line"||Le(m.value))return;const S=T[R.currentIndex].$el,{lineWidth:B,lineHeight:E}=e,G=S.offsetLeft+S.offsetWidth/2,fe={width:Nt(B),backgroundColor:e.color,transform:`translateX(${G}px) translateX(-50%)`};if(c&&(fe.transitionDuration=`${e.duration}s`),ft(E)){const Ce=Nt(E);fe.height=Ce,fe.borderRadius=Ce}R.lineStyle=fe})},j=c=>{const T=c<R.currentIndex?-1:1;for(;c>=0&&c<w.length;){if(!w[c].disabled)return c;c+=T}},ne=(c,T)=>{const S=j(c);if(!ft(S))return;const B=w[S],E=H(B,S),G=R.currentIndex!==null;R.currentIndex!==S&&(R.currentIndex=S,T||W(),K()),E!==e.active&&(l("update:active",E),G&&l("change",E,B.title)),t&&!e.scrollspy&&Wa(Math.ceil(Pt(m.value)-ee.value))},Y=(c,T)=>{const S=w.find((E,G)=>H(E,G)===c),B=S?w.indexOf(S):0;ne(B,T)},ce=(c=!1)=>{if(e.scrollspy){const T=w[R.currentIndex].$el;if(T&&I.value){const S=Pt(T,I.value)-L.value;a=!0,i&&i(),i=un(I.value,S,c?0:+e.duration,()=>{a=!1})}}},Te=(c,T,S)=>{const{title:B,disabled:E}=w[T],G=H(w[T],T);E||(Fa(e.beforeChange,{args:[G],done:()=>{ne(T),ce()}}),Ja(c)),l("clickTab",{name:G,title:B,event:S,disabled:E})},Se=c=>{t=c.isFixed,l("scroll",c)},ze=c=>{ge(()=>{Y(c),ce(!0)})},Ve=()=>{for(let c=0;c<w.length;c++){const{top:T}=De(w[c].$el);if(T>L.value)return c===0?0:c-1}return w.length-1},Ae=()=>{if(e.scrollspy&&!a){const c=Ve();ne(c)}},Me=()=>{if(e.type==="line"&&w.length)return p("div",{class:ot("line"),style:R.lineStyle},null)},g=()=>{var c,T,S;const{type:B,border:E,sticky:G}=e,fe=[p("div",{ref:G?void 0:d,class:[ot("wrap"),{[qa]:B==="line"&&E}]},[p("div",{ref:b,role:"tablist",class:ot("nav",[B,{shrink:e.shrink,complete:le.value}]),style:z.value,"aria-orientation":"horizontal"},[(c=s["nav-left"])==null?void 0:c.call(s),w.map(Ce=>Ce.renderTitle(Te)),Me(),(T=s["nav-right"])==null?void 0:T.call(s)])]),(S=s["nav-bottom"])==null?void 0:S.call(s)];return G?p("div",{ref:d},[fe]):fe},D=()=>{K(),ge(()=>{var c,T;W(!0),(T=(c=$.value)==null?void 0:c.swipeRef.value)==null||T.resize()})};ae(()=>[e.color,e.duration,e.lineWidth,e.lineHeight],K),ae(ht,D),ae(()=>e.active,c=>{c!==J.value&&Y(c)}),ae(()=>w.length,()=>{R.inited&&(Y(e.active),K(),ge(()=>{W(!0)}))});const U=()=>{Y(e.active,!0),ge(()=>{R.inited=!0,d.value&&(o=De(d.value).height),W(!0)})},Q=(c,T)=>l("rendered",c,T);return Fe({resize:D,scrollTo:ze}),Kt(K),oa(K),Zt(U),ia(m,K),mt("scroll",Ae,{target:I,passive:!0}),q({id:k,props:e,setLine:K,scrollable:le,onRendered:Q,currentName:J,setTitleRefs:A,scrollIntoView:W}),()=>p("div",{ref:m,class:ot([e.type])},[e.showHeader?e.sticky?p(yn,{container:m.value,offsetTop:ee.value,onScroll:Se},{default:()=>[g()]}):g():null,p(kn,{ref:$,count:w.length,inited:R.inited,animated:e.animated,duration:e.duration,swipeable:e.swipeable,lazyRender:e.lazyRender,currentIndex:R.currentIndex,onChange:ne},{default:()=>{var c;return[(c=s.default)==null?void 0:c.call(s)]}})])}});const Tn=Symbol(),[Sn,Mt]=Re("tab"),Cn=$e({name:Sn,props:{id:String,dot:Boolean,type:String,color:String,title:String,badge:be,shrink:Boolean,isActive:Boolean,disabled:Boolean,controls:String,scrollable:Boolean,activeColor:String,inactiveColor:String,showZeroBadge:pe},setup(e,{slots:l}){const s=M(()=>{const a={},{type:t,color:v,disabled:i,isActive:m,activeColor:b,inactiveColor:d}=e;v&&t==="card"&&(a.borderColor=v,i||(m?a.backgroundColor=v:a.color=v));const k=m?b:d;return k&&(a.color=k),a}),o=()=>{const a=p("span",{class:Mt("text",{ellipsis:!e.scrollable})},[l.title?l.title():e.title]);return e.dot||ft(e.badge)&&e.badge!==""?p(Ka,{dot:e.dot,content:e.badge,showZero:e.showZeroBadge},{default:()=>[a]}):a};return()=>p("div",{id:e.id,role:"tab",class:[Mt([e.type,{grow:e.scrollable&&!e.shrink,shrink:e.shrink,active:e.isActive,disabled:e.disabled}])],style:s.value,tabindex:e.disabled?void 0:e.isActive?0:-1,"aria-selected":e.isActive,"aria-disabled":e.disabled||void 0,"aria-controls":e.controls,"data-allow-mismatch":"attribute"},[o()])}}),[$n,Rn]=Re("swipe-item");var An=$e({name:$n,setup(e,{slots:l}){let s;const o=qe({offset:0,inited:!1,mounted:!1}),{parent:a,index:t}=la(ca);if(!a)return;const v=M(()=>{const b={},{vertical:d}=a.props;return a.size.value&&(b[d?"height":"width"]=`${a.size.value}px`),o.offset&&(b.transform=`translate${d?"Y":"X"}(${o.offset}px)`),b}),i=M(()=>{const{loop:b,lazyRender:d}=a.props;if(!d||s)return!0;if(!o.mounted)return!1;const $=a.activeIndicator.value,k=a.count.value-1,I=$===0&&b?k:$-1,x=$===k&&b?0:$+1;return s=t.value===$||t.value===I||t.value===x,s}),m=b=>{o.offset=b};return lt(()=>{ge(()=>{o.mounted=!0})}),Fe({setOffset:m}),()=>{var b;return p("div",{class:Rn(),style:v.value},[i.value?(b=l.default)==null?void 0:b.call(l):null])}}});const On=We(An),[Bn,ct]=Re("tab"),En=ta({},Ya,{dot:Boolean,name:be,badge:be,title:String,disabled:Boolean,titleClass:Xa,titleStyle:[String,Object],showZeroBadge:pe});var Nn=$e({name:Bn,props:En,setup(e,{slots:l}){const s=sa(),o=y(!1),a=Wt(),{parent:t,index:v}=la(da);if(!t)return;const i=()=>{var x;return(x=e.name)!=null?x:v.value},m=()=>{o.value=!0,t.props.lazyRender&&ge(()=>{t.onRendered(i(),e.title)})},b=M(()=>{const x=i()===t.currentName.value;return x&&!o.value&&m(),x}),d=y(""),$=y("");wa(()=>{const{titleClass:x,titleStyle:A}=e;d.value=x?ut(x):"",$.value=A&&typeof A!="string"?xa(Ta(A)):A});const k=x=>p(Cn,Ia({key:s,id:`${t.id}-${v.value}`,ref:t.setTitleRefs(v.value),style:$.value,class:d.value,isActive:b.value,controls:s,scrollable:t.scrollable.value,activeColor:t.props.titleActiveColor,inactiveColor:t.props.titleInactiveColor,onClick:A=>x(a.proxy,v.value,A)},Ut(t.props,["type","color","shrink"]),Ut(e,["dot","badge","title","disabled","showZeroBadge"])),{title:l.title}),I=y(!b.value);return ae(b,x=>{x?I.value=!1:je(()=>{I.value=!0})}),ae(()=>e.title,()=>{t.setLine(),t.scrollIntoView()}),Sa(Tn,b),Fe({id:s,renderTitle:k}),()=>{var x;const A=`${t.id}-${v.value}`,{animated:w,swipeable:q,scrollspy:R,lazyRender:le}=t.props;if(!l.default&&!w)return;const z=R||b.value;if(w||q)return p(On,{id:s,role:"tabpanel",class:ct("panel-wrapper",{inactive:I.value}),tabindex:b.value?0:-1,"aria-hidden":!b.value,"aria-labelledby":A,"data-allow-mismatch":"attribute"},{default:()=>{var ee;return[p("div",{class:ct("panel")},[(ee=l.default)==null?void 0:ee.call(l)])]}});const J=o.value||R||!le?(x=l.default)==null?void 0:x.call(l):null;return _a(p("div",{id:s,role:"tabpanel",class:ct("panel"),tabindex:z?0:-1,"aria-labelledby":A,"data-allow-mismatch":"attribute"},[J]),[[ka,z]])}}});const Ht=We(Nn),Pn=We(xn);async function Un(e,l){const s=e.getReader();let o;for(;!(o=await s.read()).done;)l(o.value)}function Dn(e){let l,s,o,a=!1;return function(v){l===void 0?(l=v,s=0,o=-1):l=Vn(l,v);const i=l.length;let m=0;for(;s<i;){a&&(l[s]===10&&(m=++s),a=!1);let b=-1;for(;s<i&&b===-1;++s)switch(l[s]){case 58:o===-1&&(o=s-m);break;case 13:a=!0;case 10:b=s;break}if(b===-1)break;e(l.subarray(m,b),o),m=s,o=-1}m===i?l=void 0:m!==0&&(l=l.subarray(m),s-=m)}}function zn(e,l,s){let o=jt();const a=new TextDecoder;return function(v,i){if(v.length===0)s==null||s(o),o=jt();else if(i>0){const m=a.decode(v.subarray(0,i)),b=i+(v[i+1]===32?2:1),d=a.decode(v.subarray(b));switch(m){case"data":o.data=o.data?o.data+`
`+d:d;break;case"event":o.event=d;break;case"id":e(o.id=d);break;case"retry":const $=parseInt(d,10);isNaN($)||l(o.retry=$);break}}}}function Vn(e,l){const s=new Uint8Array(e.length+l.length);return s.set(e),s.set(l,e.length),s}function jt(){return{data:"",event:"",id:"",retry:void 0}}var Mn=function(e,l){var s={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&l.indexOf(o)<0&&(s[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,o=Object.getOwnPropertySymbols(e);a<o.length;a++)l.indexOf(o[a])<0&&Object.prototype.propertyIsEnumerable.call(e,o[a])&&(s[o[a]]=e[o[a]]);return s};const vt="text/event-stream",Hn=1e3,Lt="last-event-id";function qt(e,l){var{signal:s,headers:o,onopen:a,onmessage:t,onclose:v,onerror:i,openWhenHidden:m,fetch:b}=l,d=Mn(l,["signal","headers","onopen","onmessage","onclose","onerror","openWhenHidden","fetch"]);return new Promise(($,k)=>{const I=Object.assign({},o);I.accept||(I.accept=vt);let x;function A(){x.abort(),document.hidden||H()}m||document.addEventListener("visibilitychange",A);let w=Hn,q=0;function R(){document.removeEventListener("visibilitychange",A),window.clearTimeout(q),x.abort()}s==null||s.addEventListener("abort",()=>{R(),$()});const le=b??window.fetch,z=a??jn;async function H(){var J;x=new AbortController;try{const ee=await le(e,Object.assign(Object.assign({},d),{headers:I,signal:x.signal}));await z(ee),await Un(ee.body,Dn(zn(L=>{L?I[Lt]=L:delete I[Lt]},L=>{w=L},t))),v==null||v(),R(),$()}catch(ee){if(!x.signal.aborted)try{const L=(J=i==null?void 0:i(ee))!==null&&J!==void 0?J:w;window.clearTimeout(q),q=window.setTimeout(H,L)}catch(L){R(),k(L)}}}H()})}function jn(e){const l=e.headers.get("content-type");if(!(l!=null&&l.startsWith(vt)))throw new Error(`Expected content-type to be ${vt}, Actual: ${l}`)}const Ln={class:"bg-[#f3f8fa] p-2 overflow-auto",style:{height:"calc(100vh - 190px)"}},qn={class:"pc_container",style:{display:"flex"}},Wn={class:"bg-[#fff] m_bg",style:{"border-radius":"10px",width:"40%",height:"calc(100vh - 190px)"}},Fn={class:"p-3",style:{display:"flex","justify-content":"space-between"}},Jn={class:"pc_right bg-[#fff]"},Kn={id:"typing-area"},Yn={key:0,class:"decContaniner nop bg-[#fff]"},Xn={key:0,class:"img_box"},Zn=["src"],Qn={key:1,class:"icon"},Gn={class:"process_text label_width"},eo={key:0,class:"process"},to={key:0,class:"img_box"},ao=["src"],no={key:1,class:"icon"},oo={class:"process"},lo={class:"process_text"},so={key:1},io=["src"],ro=["src"],co={class:"mobile_container"},uo={class:"p-3",style:{display:"flex","justify-content":"space-between"}},fo={class:"mobile_right"},vo={id:"typing-area"},mo={key:0,class:"decContaniner nop bg-[#fff]"},ho={key:0,class:"img_box"},go=["src"],yo={key:1,class:"icon"},po={class:"process_text label_width"},bo={key:0,class:"img_box"},wo=["src"],_o={key:1,class:"icon"},ko={class:"process"},Io={class:"process_text"},xo={key:1},To={__name:"[appUuid]",props:{currentItem:{type:Object,default:()=>{}},subStatusDetail:{type:Object,default:()=>{}}},async setup(e,{expose:l}){var It,xt,Tt,St,Ct,$t,Rt;let s,o;const a=e,t=Dt("loading.png"),v=Dt("copy.png"),i=qe({}),m=Ca(),b={},d=y([]),$=y(a.subStatusDetail),k=y(a.currentItem),I=y(me.get("userInfo")?JSON.parse(me.get("userInfo")):{}),x=y(null),{t:A,locale:w}=$a();Ra();const q=y(!1);let R=y("a"),le=y(!0),z=y("");const H=y(""),J=y(null),ee=y(null),L=y(["1","2"]),W=y(!1),K=y(!1),j=y(!1);let ne;const Y=y(""),ce=y(""),Te=y(""),Se=Ot("ai_apps_lang",{domain:".medon.com.cn",maxAge:30*24*60*60*12});l({subScript:async()=>{w.value=="zh"&&($.value=await Na(),k.value=a.currentItem),j.value=!0}});const Ve=async()=>{var r;await Ua({appId:Y.value,user:I.value.userName,mode:(r=x.value)==null?void 0:r.mode,task_id:H.value}),setTimeout(()=>{ke.abort(),_e=!0,we.value=[],W.value=!1,ue.value.length&&ue.value.forEach(n=>{n.status=!0}),Be()},0)},Ae=()=>{j.value=!1},Me=async(r,n)=>{var X;let h=Et(w.value);if(!((X=I.value)!=null&&X.userId))!h||h=="zh"?window.addLoginDom():location.href=location.origin+"/"+w.value+"/login";else{const F={appUuid:n,priceId:r.priceId,monthNum:r.monthNum};let re=await Da(F);re&&(he({type:"success",message:A("tool.sS")}),setTimeout(()=>{location.href=re},1e3))}},{data:g}=([s,o]=Aa(async()=>nn("parameters",async()=>{var F;const r=Ot("userInfo");Se.value=w.value;const n=on(),h=await Bt(m.params.appUuid,n);return Y.value=h==null?void 0:h.dAppUuid,ce.value=`${h==null?void 0:h.appName}-${tn[Se.value]}`,await Oa({appId:Y.value,user:(F=r==null?void 0:r.value)==null?void 0:F.userName},n)},{server:!0})),s=await s,o(),s);(It=g.value)!=null&&It.user_input_form&&(d.value=g.value.user_input_form,g.value.user_input_form.forEach(r=>{const n=Object.keys(r)[0],h=r[n].variable;b[h]={label:r[n].label},i[h]=""})),lt(async()=>{const r=window.innerHeight/100;document.documentElement.style.setProperty("--vh",`${r}px`),fe();const n=await Bt(m.params.appUuid);Te.value=n==null?void 0:n.appUuid,Y.value=n==null?void 0:n.dAppUuid,me.get("userInfo")&&await _t(),Za(n==null?void 0:n.customCss,n==null?void 0:n.customJs)}),ln({title:ce.value,meta:[{name:"keywords",content:`${an.toolKeywords[Se.value]},${(xt=a==null?void 0:a.currentItem)==null?void 0:xt.appName}`},{name:"description",content:(Tt=a==null?void 0:a.currentItem)==null?void 0:Tt.appDescription},{property:"og:type",content:"website"},{property:"og:title",content:ce.value},{property:"og:description",content:(St=a==null?void 0:a.currentItem)==null?void 0:St.appDescription},{property:"og:image",content:(Ct=a==null?void 0:a.currentItem)==null?void 0:Ct.appIcon},{name:"twitter:card",content:"summary_large_image"},{name:"twitter:title",content:ce.value},{name:"twitter:description",content:($t=a==null?void 0:a.currentItem)==null?void 0:$t.appDescription},{name:"twitter:image",content:(Rt=a==null?void 0:a.currentItem)==null?void 0:Rt.appIcon}]});const D=r=>{R.value=r.name},U=async()=>{j.value=!0},Q=M(()=>!!d.value.length),c=M(()=>d.value.filter(r=>{const n=Object.keys(r)[0];return r[n].required===!0}).map(r=>{const n=Object.keys(r)[0];return r[n].variable})),T=()=>{Y.value&&Pa({appId:Y.value,user:I.value.userName}).then(r=>{x.value={...r}})},S=y(!1),B=y(!1),E=y(!1),G=y(!1),fe=async()=>{var n,h;let r=[{userRandomId:Math.random().toString(36).substr(2,9)+Date.now().toString(36),title:document.title,refer:"",userAgen:navigator.userAgent,time:new Date().getTime(),url:location.href,actionValue:"",userAction:"Exposure",actionCode:null,userId:(n=I.value)==null?void 0:n.userId,userToken:"",channel:"MedSci_xAI",appId:Y.value,userUuid:(h=I.value)==null?void 0:h.openid}];await Ba.post("https://app-trace.medsci.cn/api/points/v1/user-action-batch",r)},Ce=r=>{j.value=r},fa=r=>Object.values(r).some(n=>n),gt=async()=>{var r,n,h,X,F,re;if(!me.get("userInfo")){me.remove("yudaoToken",{domain:"ai.medsci.cn"}),me.remove("yudaoToken",{domain:"ai.medon.com.cn"}),me.remove("yudaoToken",{domain:".medsci.cn"}),me.remove("yudaoToken",{domain:".medon.com.cn"}),me.remove("yudaoToken",{domain:"localhost"}),localStorage.removeItem("hasuraToken");const se=Et(w.value);!se||se=="zh"?window.addLoginDom():location.href=location.origin+"/"+w.value+"/login";return}if(await _t(),!((n=(r=a.currentItem)==null?void 0:r.appUser)!=null&&n.status)||((X=(h=a.currentItem)==null?void 0:h.appUser)==null?void 0:X.status)==2)return j.value=!0,!1;if(c.value.length==0&&!fa(i)){he({message:`${A("tool.enterquestion")}`,type:"error"});return}for(let se in i)if(c.value.includes(se)&&!i[se]){he({message:`${b[se].label}${A("tool.requiredfield")}`,type:"error"});return}(F=x.value)!=null&&F.mode&&(["advanced-chat","chat"].includes((re=x.value)==null?void 0:re.mode)?he({type:"success",message:A("tool.planning")}):x.value.mode=="completion"?(le.value=!1,setTimeout(()=>{R.value="b"},1e3),ma()):(le.value=!1,setTimeout(()=>{R.value="b"},1e3),va()))},we=y([]);var ue=y([]),Je=y([]);const Ke=y(""),Ye=y(0),ve=y(""),Xe=y(!1),Oe=y(!1);let Ze=y(0);const Qe=y(!1),ie=y(!1);let _e=!1,yt=!1,ke;ae(ue,()=>{pt()},{deep:!0});const pt=()=>{Ze.value<ue.value.length&&(Je.value.push(ue.value[Ze.value]),Ze.value++,setTimeout(pt,1e3))},bt=()=>{Ye.value<Ke.value.length?(Xe.value=!0,ve.value+=Ke.value.charAt(Ye.value),Ye.value++,setTimeout(bt,20)):(ie.value=!1,Xe.value=!1,E.value=!0,Be())},va=async()=>{K.value=!0,W.value=!0,z.value="",ue.value=[],Je.value=[],Ze.value=0,ve.value="",Ke.value="",we.value=[],Qe.value=!1,_e=!1,Ye.value=0,ke=new AbortController;try{let r=`${window.location.origin}/dev-api/ai-base/v1/workflows/run`;S.value=!0,B.value=!0,G.value=!1,await qt(r,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${et.get("yudaoToken")||null}`},body:JSON.stringify({appId:Y.value,user:I.value.userName,inputs:{...i,outputLanguage:i.outputLanguage?i.outputLanguage:st(w.value)=="中文"?"简体中文":st(w.value)},requestId:crypto.randomUUID(),files:[],response_mode:"streaming",appUuid:Te.value}),onmessage(n){var h,X,F,re,se,He,Ge,V,de,f;if(n.data.trim())try{const _=JSON.parse(n.data);if(H.value=_.task_id,_.error)throw new Error(_.error);if(((h=_==null?void 0:_.data)==null?void 0:h.title)=="智能体推理思维链"&&_.event==="node_finished"&&(ie.value=!0,Ke.value=(re=JSON.parse((F=(X=_==null?void 0:_.data)==null?void 0:X.outputs)==null?void 0:F.text))==null?void 0:re.text,bt()),_.event==="node_started"&&!Qe.value&&((se=_==null?void 0:_.data)==null?void 0:se.title)!="开始"&&ue.value.push({node_id:(He=_==null?void 0:_.data)==null?void 0:He.node_id,title:(Ge=_==null?void 0:_.data)==null?void 0:Ge.title,status:!1}),_.event==="error"){if(_.code==5052){setTimeout(()=>{ke.abort()},0),W.value=!1,S.value=!1,E.value=!1,B.value=!1,ie.value=!1,he.error(_.message);return}_.code==5047&&(Oe.value=!0),Qe.value=!0,W.value=!1,_e=!0,B.value=!1,z.value=_==null?void 0:_.message}_.event==="node_finished"&&ue.value.forEach(Ne=>{var At;Ne.node_id==((At=_==null?void 0:_.data)==null?void 0:At.node_id)&&(Ne.status=!0)}),_.event==="text_chunk"&&(q.value=!0,we.value.push((V=_==null?void 0:_.data)==null?void 0:V.text),ie.value||Be()),_.event==="workflow_started"&&(S.value=!1),_.event==="workflow_finished"&&(_e=!0,Qe.value=!0,W.value=!1,B.value=!1,E.value=!1,q.value||(we.value.push((f=(de=_==null?void 0:_.data)==null?void 0:de.outputs)==null?void 0:f.text),ie.value||Be()))}catch(_){Ee(_)}},onerror(n){Ee(n)},signal:ke.signal,openWhenHidden:!0})}catch{Ee()}},ma=async()=>{z.value="",we.value=[],ie.value=!1,_e=!1,ke=new AbortController;try{let r=`${window.location.origin}/dev-api/ai-base/v1/completion-messages`;S.value=!0,B.value=!0,G.value=!1,await qt(r,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${et.get("yudaoToken")||null}`},body:JSON.stringify({appId:Y.value,user:I.value.userName,inputs:{...i,outputLanguage:st()},files:[],response_mode:"streaming",appUuid:Te.value,requestId:crypto.randomUUID()}),onmessage(n){if(S.value=!1,E.value=!0,n.data.trim())try{const h=JSON.parse(n.data);if(H.value=h.task_id,h.error)throw new Error(h.error);if(h.event==="error"){if(h.code==5052){setTimeout(()=>{ke.abort()},0),W.value=!1,S.value=!1,E.value=!1,B.value=!1,ie.value=!1,he.error(h.message);return}h.code==5047&&(Oe.value=!0),_e=!0,z.value=h==null?void 0:h.message,E.value=!1,B.value=!1}h.event==="message"&&(we.value.push(h==null?void 0:h.answer),ie.value||Be()),h.event==="message_end"&&(_e=!0,B.value=!1,E.value=!1)}catch(h){Ee(h)}},onerror(n){Ee(n)},signal:ke.signal,openWhenHidden:!0})}catch{Ee()}},Be=()=>{if(we.value.length===0){ie.value=!1,yt=!0,ha();return}ie.value=!0;const r=we.value.shift();ga(r).then(()=>{Be()})},ha=()=>{yt&&_e&&(B.value=!1,E.value=!1,G.value=!0)},ga=r=>new Promise(n=>{let h=0;ne=sn(()=>{if(h<(r==null?void 0:r.length)){z.value+=r[h++];const X=document.getElementsByClassName("pc_right");X[0].scrollTop=X[0].scrollHeight;const F=document.getElementsByClassName("mobile_right");F[0].scrollTop=F[0].scrollHeight}else clearInterval(ne),n()},0)}),Ee=()=>{setTimeout(()=>{ke.abort()},0),W.value=!1,S.value=!1,E.value=!1,B.value=!1,ie.value=!1,he.error(A("tool.accessbusy")),z.value=A("tool.accessbusy")},ya=async()=>{try{await navigator.clipboard.writeText(z.value),he({type:"success",message:A("tool.copysuccess")})}catch(r){he(r)}},wt=()=>{for(let r in i)i[r]="";J.value.forEach(r=>{r.updateMessage()}),ee.value.forEach(r=>{r.updateMessage()})},_t=async()=>{if(et.get("yudaoToken")){T();return}try{await Ea({userId:I.value.userId,userName:I.value.userName,realName:I.value.realName,avatar:I.value.avatar,plaintextUserId:I.value.plaintextUserId,mobile:I.value.mobile,email:I.value.email}).then(async n=>{n!=null&&n.token&&(n!=null&&n.htoken)?(et.set("yudaoToken",n.token),localStorage.setItem("hasuraToken",n.htoken),localStorage.setItem("openid",n.openid),localStorage.setItem("socialUserId",n.socialUserId),localStorage.setItem("socialType",n.socialType),T()):console.error("登录失败: 未返回 token")})}catch(n){console.error(n,"登录失败pc")}},kt=()=>{};return(r,n)=>{const h=Pe("el-button"),X=Pe("el-icon"),F=Pe("el-collapse-item"),re=Pe("el-collapse"),se=Pe("v-md-preview"),He=pa,Ge=Pe("el-dialog");return C(),O(xe,null,[N("div",Ln,[N("div",qn,[u(Q)?(C(),O(xe,{key:0},[N("div",Wn,[(C(!0),O(xe,null,Ue(u(d),(V,de)=>(C(),O("div",{class:"flex",key:de},[(C(!0),O(xe,null,Ue(V,(f,_)=>(C(),Ie(zt,{key:f.variable,type:_,onPayShowStatus:Ce,label:f==null?void 0:f.label,value:u(i)[f==null?void 0:f.variable],required:f==null?void 0:f.required,placeholder:f==null?void 0:f.label,max_length:f==null?void 0:f.max_length,options:f==null?void 0:f.options,fileVerify:f==null?void 0:f.allowed_file_types,currentItem:a.currentItem,"onUpdate:value":Ne=>u(i)[f==null?void 0:f.variable]=Ne,ref_for:!0,ref_key:"childRef",ref:J},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","currentItem","onUpdate:value"]))),128))]))),128)),N("div",Fn,[p(h,{onClick:wt},{default:P(()=>[oe(Z(r.$t("tool.clear")),1)]),_:1}),p(h,{onClick:gt,loading:u(B),type:"primary"},{default:P(()=>[oe(Z(r.$t("tool.execute")),1)]),_:1},8,["loading"])])]),N("div",Jn,[N("div",Kn,[u(Je).length>0||u(ve)||u(K)?(C(),O("div",Yn,[p(re,{modelValue:u(L),"onUpdate:modelValue":n[0]||(n[0]=V=>at(L)?L.value=V:null)},{default:P(()=>[p(F,{name:"1"},{title:P(()=>[u(W)?(C(),O("div",Xn,[N("img",{src:u(t),alt:"loading"},null,8,Zn)])):(C(),O("div",Qn,[p(X,null,{default:P(()=>[p(u(tt))]),_:1})])),oe(" "+Z(r.$t("tool.execution_progress")),1)]),default:P(()=>[(C(!0),O(xe,null,Ue(u(Je),(V,de)=>(C(),O("div",{key:de,class:"process"},[N("div",Gn,Z(V.title),1),n[5]||(n[5]=oe("    ")),N("span",{style:{color:"#36b15e"},class:ut(V.status?"":"loading-text")},Z(V.status?r.$t("tool.completed"):r.$t("tool.loading")),3)]))),128))]),_:1}),N("div",null,[u(ve)?(C(),O("div",eo)):te("",!0)]),u(ve)?(C(),Ie(F,{key:0,name:"2"},{title:P(()=>[u(Xe)?(C(),O("div",to,[N("img",{src:u(t),alt:"loading"},null,8,ao)])):(C(),O("div",no,[p(X,null,{default:P(()=>[p(u(tt))]),_:1})])),oe(" "+Z(r.$t("tool.reasoning_process")),1)]),default:P(()=>[N("div",oo,[N("div",lo,Z(u(ve)),1)])]),_:1})):te("",!0)]),_:1},8,["modelValue"])])):te("",!0),p(He,null,{default:P(()=>[u(z)&&!u(Oe)?(C(),Ie(se,{key:0,text:u(z),id:"previewMd"},null,8,["text"])):te("",!0)]),_:1}),u(Oe)?(C(),O("div",so,[oe(Z(u(z))+" ",1),p(h,{type:"text",onClick:U},{default:P(()=>[oe(Z(r.$t("market.subscribe")),1)]),_:1})])):te("",!0),N("div",null,[u(E)?(C(),O("img",{key:0,src:u(t),alt:"loading",class:"spinner"},null,8,io)):te("",!0),u(E)?(C(),O("span",{key:1,text:"",type:"primary",class:"stop_btn",onClick:Ve},Z(r.$t("tool.stopGeneration")),1)):te("",!0),u(G)?(C(),O("img",{key:2,onClick:ya,src:u(v),alt:"",style:{width:"20px"},class:"copy"},null,8,ro)):te("",!0)])])])],64)):te("",!0)]),N("div",co,[p(u(Pn),{active:u(R),shrink:"","line-width":"20",onClickTab:D},{default:P(()=>[p(u(Ht),{title:"输入",name:"a"},{default:P(()=>[(C(!0),O(xe,null,Ue(u(d),(V,de)=>(C(),O("div",{class:"flex",key:de},[(C(!0),O(xe,null,Ue(V,(f,_)=>(C(),Ie(zt,{key:f.variable,type:_,onPayShowStatus:Ce,label:f==null?void 0:f.label,value:u(i)[f==null?void 0:f.variable],required:f==null?void 0:f.required,placeholder:f==null?void 0:f.label,max_length:f==null?void 0:f.max_length,options:f==null?void 0:f.options,fileVerify:f==null?void 0:f.allowed_file_types,currentItem:a.currentItem,"onUpdate:value":Ne=>u(i)[f==null?void 0:f.variable]=Ne,ref_for:!0,ref_key:"childRef",ref:J},null,8,["type","label","value","required","placeholder","max_length","options","fileVerify","currentItem","onUpdate:value"]))),128))]))),128)),N("div",uo,[p(h,{onClick:wt},{default:P(()=>n[6]||(n[6]=[oe("Clear")])),_:1}),p(h,{onClick:n[1]||(n[1]=V=>gt("mobile")),loading:u(B),type:"primary"},{default:P(()=>n[7]||(n[7]=[oe("Execute")])),_:1},8,["loading"])])]),_:1}),p(u(Ht),{title:"结果",name:"b",disabled:u(le)},{default:P(()=>[N("div",fo,[N("div",vo,[u(ue).length>0||u(ve)?(C(),O("div",mo,[p(re,{modelValue:u(L),"onUpdate:modelValue":n[2]||(n[2]=V=>at(L)?L.value=V:null)},{default:P(()=>[p(F,{name:"1"},{title:P(()=>[u(W)?(C(),O("div",ho,[N("img",{src:u(t),alt:"loading"},null,8,go)])):(C(),O("div",yo,[p(X,null,{default:P(()=>[p(u(tt))]),_:1})])),oe(" "+Z(r.$t("tool.execution_progress")),1)]),default:P(()=>[(C(!0),O(xe,null,Ue(u(ue),(V,de)=>(C(),O("div",{key:de,class:"process"},[N("div",po,Z(V.title),1),n[8]||(n[8]=oe("    ")),N("span",{style:{color:"#36b15e"},class:ut(V.status?"":"loading-text")},Z(V.status?r.$t("tool.completed"):r.$t("tool.loading")),3)]))),128))]),_:1}),n[9]||(n[9]=N("div",null,[N("div",{class:"process"})],-1)),u(ve)?(C(),Ie(F,{key:0,title:"推导过程",name:"2"},{title:P(()=>[u(Xe)?(C(),O("div",bo,[N("img",{src:u(t),alt:"loading"},null,8,wo)])):(C(),O("div",_o,[p(X,null,{default:P(()=>[p(u(tt))]),_:1})])),oe(" "+Z(r.$t("tool.reasoning_process")),1)]),default:P(()=>[N("div",ko,[N("div",Io,Z(u(ve)),1)])]),_:1})):te("",!0)]),_:1},8,["modelValue"])])):te("",!0),p(He,null,{default:P(()=>[u(z)&&!u(Oe)?(C(),Ie(se,{key:0,text:u(z),id:"previewMd"},null,8,["text"])):te("",!0)]),_:1}),u(Oe)?(C(),O("div",xo,[oe(Z(u(z))+" ",1),p(h,{type:"text",onClick:U},{default:P(()=>[oe(Z(r.$t("market.subscribe")),1)]),_:1})])):te("",!0)])])]),_:1},8,["disabled"])]),_:1},8,["active"])]),u(j)?(C(),Ie(Ge,{key:0,modelValue:u(j),"onUpdate:modelValue":n[3]||(n[3]=V=>at(j)?j.value=V:null),class:"payPC","show-close":!1},{default:P(()=>[p(Qa,{userInfo:u(I),currentItem:u(k),onToAgreement:kt,subStatusDetail:u($),onClose:Ae,onSubscribe:Me},null,8,["userInfo","currentItem","subStatusDetail"])]),_:1},8,["modelValue"])):te("",!0),u(j)?(C(),Ie(u(en),{key:1,show:u(j),"onUpdate:show":n[4]||(n[4]=V=>at(j)?j.value=V:null),round:"",closeable:"",class:"payMobile",position:"bottom",style:{height:"90%"}},{default:P(()=>[p(Ga,{userInfo:u(I),subStatusDetail:u($),currentItem:u(k),onToAgreement:kt,onClose:Ae},null,8,["userInfo","subStatusDetail","currentItem"])]),_:1},8,["show"])):te("",!0)]),n[10]||(n[10]=N("footer",{class:"text-gray-400 w-full bottom-0 text-sm text-center h-8 leading-8"}," 内容由 AI 生成, 仅供参考 ",-1))],64)}}},Po=rn(To,[["__scopeId","data-v-93580b51"]]);export{Po as default};
