package cn.iocoder.yudao.aiBase.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 主站用户 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MedsciUsersRespVO {

    private Long id;

    @Schema(description = "三方类型", example = "0主站 35谷歌 36facebook")
    @DictFormat("ai_base_social_type")
    private Integer socialType;

    @Schema(description = "三方ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "4708")
    @ExcelProperty("三方ID")
    private Long socialUserId;

    @Schema(description = "三方openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "7001")
    @ExcelProperty("三方openid")
    private String openid;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "用户名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("用户名")
    private String userName;

    @Schema(description = "真实姓名", example = "赵六")
    @ExcelProperty("真实姓名")
    private String realName;

    @Schema(description =  "头像")
    @ExcelProperty("头像")
    private String avatar;

    @Schema(description =  "手机号")
    private String mobile;

    @Schema(description =  "客户id")
    private String stripeCustomerId;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    @Schema(description = "1启用，2禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty(value = "1启用，2禁用", converter = DictConvert.class)
    @DictFormat("user_status_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "过期时间")
    @ExcelProperty("过期时间")
    private LocalDateTime expireAt;

    @Schema(description = "是否是内部用户 0否 1是")
    private Integer isInternalUser;

}
