package cn.iocoder.yudao.aiBase.mapper;

import cn.iocoder.yudao.aiBase.entity.AiAppKnowledgeFile;
import cn.iocoder.yudao.aiBase.dto.param.KnowledgeParam;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.aiBase.config.BaseConstant;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AiAppKnowledgeFileMapper extends BaseMapperX<AiAppKnowledgeFile> {

    /**
     * 根据参数查询知识库文件列表
     *
     * @param reqVO 查询参数
     * @return 知识库文件列表
     */
    default List<AiAppKnowledgeFile> selectList(KnowledgeParam reqVO) {
        return selectList(new LambdaQueryWrapperX<AiAppKnowledgeFile>()
            .eqIfPresent(AiAppKnowledgeFile::getSocialUserId, reqVO.getSocialUserId())
            .eqIfPresent(AiAppKnowledgeFile::getSocialType, reqVO.getSocialType())
            .eqIfPresent(AiAppKnowledgeFile::getPid, reqVO.getPid())
            .eqIfPresent(AiAppKnowledgeFile::getType, reqVO.getType())
            .likeIfPresent(AiAppKnowledgeFile::getFileName, reqVO.getFileName())
            .eq(AiAppKnowledgeFile::getDeleted, BaseConstant.ZERO)
            .orderByDesc(AiAppKnowledgeFile::getId));
    }
}