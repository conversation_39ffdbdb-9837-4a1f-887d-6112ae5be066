package cn.iocoder.yudao.aiBase.dto.request;

import cn.iocoder.yudao.aiBase.config.BaseConstant;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class UserImportExcelVO {

    @ExcelProperty("三方平台")
    @HeadStyle(fillForegroundColor = 10)
    private Integer socialType = BaseConstant.ZERO;

    @ExcelProperty("主站ID")
    @HeadStyle(fillForegroundColor = 10)
    private Long socialUserId;

    @ExcelProperty("主站加密")
    @HeadStyle(fillForegroundColor = 10)
    private String openid;

    @ExcelProperty("邮箱")
    @HeadStyle(fillForegroundColor = 10)
    private String email;

    @ExcelProperty("用户名")
    @HeadStyle(fillForegroundColor = 10)
    private String userName;

    @ExcelProperty("密码")
    private String password;

    @ExcelProperty("真实姓名")
    private String realName;

    @ExcelProperty("头像")
    private String avatar;

    @ExcelProperty("手机号")
    private String mobile;

    private String remark;
}
